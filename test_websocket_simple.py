#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 WebSocket 客户端测试脚本（同步版本）
用于测试单个学校分析的 WebSocket 服务器
"""

import json
import time
import re
import sys

try:
    from websocket import create_connection, WebSocket
    import websocket
except ImportError:
    print("❌ 缺少依赖包 websocket-client")
    print("请运行: pip install websocket-client")
    sys.exit(1)

class SimpleWebSocketTester:
    def __init__(self, uri="ws://127.0.0.1:8791"):
        self.uri = uri
        self.ws = None
        
    def connect(self):
        """连接到 WebSocket 服务器"""
        try:
            print(f"正在连接到 WebSocket 服务器: {self.uri}")
            self.ws = create_connection(self.uri, timeout=10)
            print("✅ WebSocket 连接成功!")
            return True
        except Exception as e:
            print(f"❌ WebSocket 连接失败: {e}")
            return False
    
    def send_message(self, message):
        """发送消息到服务器"""
        if not self.ws:
            print("❌ WebSocket 未连接")
            return False
            
        try:
            message_json = json.dumps(message, ensure_ascii=False)
            print(f"📤 发送消息: {message_json}")
            self.ws.send(message_json)
            return True
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            return False
    
    def receive_message(self, timeout=30):
        """接收服务器消息"""
        if not self.ws:
            print("❌ WebSocket 未连接")
            return None
            
        try:
            self.ws.settimeout(timeout)
            message = self.ws.recv()
            return message
        except Exception as e:
            print(f"❌ 接收消息失败: {e}")
            return None
    
    def handle_message(self, message):
        """处理服务器消息"""
        try:
            data = json.loads(message)
        except json.JSONDecodeError:
            print(f"📥 收到非JSON消息: {message}")
            return
        
        message_type = data.get('type', 'unknown')
        
        if message_type == 'connected':
            print(f"✅ 连接确认: {data.get('message', '')}")
            print(f"🆔 连接ID: {data.get('connection_id', '')}")
            
        elif message_type == 'start':
            print(f"🚀 开始处理: {data.get('message', '')}")
            
        elif message_type == 'data':
            content = data.get('content', '')
            print(f"📊 流式数据长度: {len(content)} 字符")
            
            # 解析 MODULE 格式数据
            self.parse_module_data(content)
            
        elif message_type == 'complete':
            print(f"✅ 处理完成: {data.get('message', '')}")
            return True  # 表示处理完成
            
        elif message_type == 'error':
            print(f"❌ 服务器错误: {data.get('message', '')}")
            return True  # 表示处理结束（虽然是错误）
            
        else:
            print(f"📥 未知消息类型 '{message_type}': {data}")
            
        return False  # 表示继续等待消息
    
    def parse_module_data(self, content):
        """解析 MODULE 格式的数据"""
        # 匹配 MODULE 格式
        module_pattern = r'\[MODULE(\d+)\](.*?)\[/MODULE\1\]'
        matches = re.findall(module_pattern, content, re.DOTALL)
        
        if matches:
            print("🔍 解析到的模块数据:")
            for module_num, module_content in matches:
                module_content = module_content.strip()
                
                if module_num == '1':
                    print(f"📋 MODULE1 - 总成绩计算公式:")
                    if '总成绩计算公式:' in module_content:
                        formula = module_content.replace('总成绩计算公式:', '').strip()
                        print(f"   {formula}")
                        
                elif module_num == '2':
                    print(f"📈 MODULE2 - 竞争难度分析:")
                    if '竞争难度分析:' in module_content:
                        analysis = module_content.replace('竞争难度分析:', '').strip()
                        print(f"   {analysis[:200]}{'...' if len(analysis) > 200 else ''}")
                        
                elif module_num == '3':
                    print(f"🎯 MODULE3 - 备考目标建议:")
                    if '备考目标建议:' in module_content:
                        suggestion = module_content.replace('备考目标建议:', '').strip()
                        print(f"   {suggestion[:200]}{'...' if len(suggestion) > 200 else ''}")
        else:
            # 如果没有匹配到 MODULE 格式，显示原始内容的一部分
            if content.strip():
                print(f"📄 原始内容: {content[:300]}{'...' if len(content) > 300 else ''}")
    
    def close(self):
        """关闭连接"""
        if self.ws:
            self.ws.close()
            print("🔌 WebSocket 连接已关闭")

def test_single_school_analysis():
    """测试单个学校分析功能"""
    tester = SimpleWebSocketTester()
    
    # 连接到服务器
    if not tester.connect():
        return
    
    # 准备测试数据
    test_message = {
        "report_id": "771",  # 测试用的报告ID
        "school_name": "安徽大学",
        "college_name": "计算机科学与技术学院"
    }
    
    try:
        # 等待连接稳定
        time.sleep(1)
        
        # 接收连接确认消息
        print("👂 等待连接确认消息...")
        message = tester.receive_message(timeout=5)
        if message:
            tester.handle_message(message)
        
        # 发送测试消息
        if not tester.send_message(test_message):
            return
        
        # 持续接收消息直到处理完成
        print("👂 等待服务器响应...")
        max_messages = 50  # 最多接收50条消息
        message_count = 0
        
        while message_count < max_messages:
            message = tester.receive_message(timeout=30)
            if message is None:
                print("⏰ 接收消息超时")
                break
                
            message_count += 1
            print(f"\n--- 消息 {message_count} ---")
            
            # 处理消息，如果返回True表示处理完成
            if tester.handle_message(message):
                print("🏁 处理流程完成")
                break
                
        if message_count >= max_messages:
            print("⚠️  达到最大消息数量限制")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    finally:
        tester.close()

def print_usage():
    """打印使用说明"""
    print("=" * 60)
    print("🧪 WebSocket 服务器测试工具 (简单版)")
    print("=" * 60)
    print("功能: 测试单个学校分析的 WebSocket 服务器")
    print("服务器地址: ws://127.0.0.1:8791")
    print("")
    print("测试数据:")
    print("  - report_id: 1")
    print("  - school_name: 安徽大学")
    print("  - college_name: 计算机科学与技术学院")
    print("")
    print("预期返回:")
    print("  - MODULE1: 总成绩计算公式")
    print("  - MODULE2: 竞争难度分析")
    print("  - MODULE3: 备考目标建议")
    print("=" * 60)
    print("")

def main():
    """主函数"""
    print_usage()
    
    print("🚀 开始测试...")
    print("按 Ctrl+C 可以随时中断测试")
    print("")
    
    test_single_school_analysis()
    
    print("")
    print("🏁 测试完成!")

if __name__ == "__main__":
    # 运行测试
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
