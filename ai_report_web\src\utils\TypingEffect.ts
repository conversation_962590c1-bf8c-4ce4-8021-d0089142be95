/**
 * 打字机效果处理类
 */
export interface TypingOptions {
  speed?: number; // 打字速度（毫秒）
  onComplete?: () => void; // 完成回调
  onProgress?: (currentText: string, progress: number) => void; // 进度回调
}

export interface TypingTarget {
  element?: HTMLElement;
  callback?: (text: string) => void;
}

export class TypingEffect {
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private defaultSpeed: number = 50;

  /**
   * 启动单个文本的打字机效果
   */
  startTyping(
    id: string,
    text: string,
    target: TypingTarget,
    options: TypingOptions = {}
  ): void {
    // 清除之前的打字机效果
    this.stopTyping(id);

    if (!text || text.trim() === '') {
      if (options.onComplete) {
        options.onComplete();
      }
      return;
    }

    const speed = options.speed || this.defaultSpeed;
    let currentIndex = 0;

    const interval = setInterval(() => {
      if (currentIndex < text.length) {
        const currentText = text.substring(0, currentIndex + 1);
        
        // 更新目标
        if (target.element) {
          target.element.textContent = currentText;
        }
        
        if (target.callback) {
          target.callback(currentText);
        }

        // 触发进度回调
        if (options.onProgress) {
          const progress = (currentIndex + 1) / text.length;
          options.onProgress(currentText, progress);
        }

        currentIndex++;
      } else {
        // 打字完成
        this.stopTyping(id);
        
        if (options.onComplete) {
          options.onComplete();
        }
        
        console.log(`打字机效果完成: ${id}`);
      }
    }, speed);

    this.intervals.set(id, interval);
  }

  /**
   * 停止指定的打字机效果
   */
  stopTyping(id: string): void {
    const interval = this.intervals.get(id);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(id);
    }
  }

  /**
   * 停止所有打字机效果
   */
  stopAllTyping(): void {
    this.intervals.forEach((interval, id) => {
      clearInterval(interval);
    });
    this.intervals.clear();
  }

  /**
   * 同时启动多个打字机效果
   */
  startMultipleTyping(
    typingTasks: Array<{
      id: string;
      text: string;
      target: TypingTarget;
      options?: TypingOptions;
    }>
  ): void {
    typingTasks.forEach(task => {
      this.startTyping(task.id, task.text, task.target, task.options);
    });
  }

  /**
   * 检查是否有正在进行的打字机效果
   */
  isTyping(id?: string): boolean {
    if (id) {
      return this.intervals.has(id);
    }
    return this.intervals.size > 0;
  }

  /**
   * 获取正在进行的打字机效果数量
   */
  getActiveTypingCount(): number {
    return this.intervals.size;
  }

  /**
   * 获取所有正在进行的打字机效果ID
   */
  getActiveTypingIds(): string[] {
    return Array.from(this.intervals.keys());
  }

  /**
   * 设置默认打字速度
   */
  setDefaultSpeed(speed: number): void {
    this.defaultSpeed = speed;
  }

  /**
   * 获取默认打字速度
   */
  getDefaultSpeed(): number {
    return this.defaultSpeed;
  }

  /**
   * 销毁打字机效果实例
   */
  destroy(): void {
    this.stopAllTyping();
  }
}

/**
 * 创建全局打字机效果实例
 */
export const globalTypingEffect = new TypingEffect();
