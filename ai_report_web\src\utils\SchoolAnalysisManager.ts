import { SingleSchoolWebSocket, type SchoolInfo, type SchoolAnalysisData } from './SingleSchoolWebSocket';
import { TypingEffect } from './TypingEffect';

/**
 * 学校分析结果接口
 */
export interface SchoolAnalysisResult {
  score_formula: string;
  difficulty_analysis: string;
  suggest: string;
}

/**
 * 学校分析管理类
 * 整合WebSocket通信和打字机效果
 */
export class SchoolAnalysisManager {
  private webSocket: SingleSchoolWebSocket;
  private typingEffect: TypingEffect;
  private isAnalyzing: boolean = false;

  // 回调函数
  private onStartCallback?: (message: string) => void;
  private onProgressCallback?: (result: SchoolAnalysisResult) => void;
  private onCompleteCallback?: (result: SchoolAnalysisResult) => void;
  private onErrorCallback?: (error: string) => void;

  constructor(wsUrl?: string) {
    this.webSocket = new SingleSchoolWebSocket(wsUrl);
    this.typingEffect = new TypingEffect();
    this.setupWebSocketCallbacks();
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks: {
    onStart?: (message: string) => void;
    onProgress?: (result: SchoolAnalysisResult) => void;
    onComplete?: (result: SchoolAnalysisResult) => void;
    onError?: (error: string) => void;
  }) {
    this.onStartCallback = callbacks.onStart;
    this.onProgressCallback = callbacks.onProgress;
    this.onCompleteCallback = callbacks.onComplete;
    this.onErrorCallback = callbacks.onError;
  }

  /**
   * 设置WebSocket回调
   */
  private setupWebSocketCallbacks() {
    this.webSocket.setCallbacks({
      onConnected: () => {
        console.log('WebSocket连接成功');
      },
      onStart: (message: string) => {
        console.log('开始分析:', message);
        if (this.onStartCallback) {
          this.onStartCallback(message);
        }
      },
      onData: (data: SchoolAnalysisData) => {
        this.handleAnalysisData(data);
      },
      onComplete: () => {
        console.log('分析完成');
        this.isAnalyzing = false;
        
        // 获取最终结果
        const finalResult = this.getCurrentResult();
        if (this.onCompleteCallback) {
          this.onCompleteCallback(finalResult);
        }
      },
      onError: (error: string) => {
        console.error('分析错误:', error);
        this.isAnalyzing = false;
        this.typingEffect.stopAllTyping();
        
        if (this.onErrorCallback) {
          this.onErrorCallback(error);
        }
      },
      onClose: () => {
        console.log('WebSocket连接关闭');
        this.isAnalyzing = false;
        this.typingEffect.stopAllTyping();
      }
    });
  }

  /**
   * 开始分析学校
   */
  async analyzeSchool(
    reportId: string | number,
    schoolInfo: SchoolInfo,
    enableTypingEffect: boolean = true
  ): Promise<void> {
    if (this.isAnalyzing) {
      throw new Error('正在进行分析，请等待完成');
    }

    this.isAnalyzing = true;
    this.typingEffect.stopAllTyping();

    try {
      await this.webSocket.analyzeSchool(reportId, schoolInfo);
    } catch (error) {
      this.isAnalyzing = false;
      throw error;
    }
  }

  /**
   * 处理分析数据
   */
  private handleAnalysisData(data: SchoolAnalysisData) {
    const result: SchoolAnalysisResult = {
      score_formula: data.score_formula,
      difficulty_analysis: data.competition_analysis,
      suggest: data.study_suggestions
    };

    console.log('收到分析数据:', result);

    // 触发进度回调
    if (this.onProgressCallback) {
      this.onProgressCallback(result);
    }
  }

  /**
   * 启动打字机效果
   */
  startTypingEffect(
    result: SchoolAnalysisResult,
    targets: {
      scoreFormula?: (text: string) => void;
      difficultyAnalysis?: (text: string) => void;
      suggest?: (text: string) => void;
    },
    options: {
      speed?: number;
      onComplete?: () => void;
    } = {}
  ) {
    const typingTasks = [];

    if (result.score_formula && targets.scoreFormula) {
      typingTasks.push({
        id: 'score_formula',
        text: result.score_formula,
        target: { callback: targets.scoreFormula },
        options: {
          speed: options.speed,
          onComplete: () => console.log('总成绩计算公式打字完成')
        }
      });
    }

    if (result.difficulty_analysis && targets.difficultyAnalysis) {
      typingTasks.push({
        id: 'difficulty_analysis',
        text: result.difficulty_analysis,
        target: { callback: targets.difficultyAnalysis },
        options: {
          speed: options.speed,
          onComplete: () => console.log('竞争难度分析打字完成')
        }
      });
    }

    if (result.suggest && targets.suggest) {
      typingTasks.push({
        id: 'suggest',
        text: result.suggest,
        target: { callback: targets.suggest },
        options: {
          speed: options.speed,
          onComplete: () => console.log('备考建议打字完成')
        }
      });
    }

    if (typingTasks.length > 0) {
      this.typingEffect.startMultipleTyping(typingTasks);
      
      // 检查所有打字机效果是否完成
      const checkAllComplete = () => {
        if (!this.typingEffect.isTyping()) {
          if (options.onComplete) {
            options.onComplete();
          }
        } else {
          setTimeout(checkAllComplete, 100);
        }
      };
      
      setTimeout(checkAllComplete, 100);
    } else if (options.onComplete) {
      options.onComplete();
    }
  }

  /**
   * 获取当前分析结果
   */
  private getCurrentResult(): SchoolAnalysisResult {
    const content = this.webSocket.getAccumulatedContent();
    const data = this.parseContent(content);
    
    return {
      score_formula: data.score_formula,
      difficulty_analysis: data.competition_analysis,
      suggest: data.study_suggestions
    };
  }

  /**
   * 解析内容
   */
  private parseContent(content: string): SchoolAnalysisData {
    const extractField = (fieldName: string): string => {
      const fieldPattern = new RegExp(`${fieldName}:\\s*"([^"]*)"`, 'i');
      const match = content.match(fieldPattern);
      
      if (match && match[1]) {
        let fieldContent = match[1];
        fieldContent = fieldContent.replace(/\\n/g, '\n');
        fieldContent = fieldContent.replace(/\\"/g, '"');
        fieldContent = fieldContent.replace(/\\\\/g, '\\');
        return fieldContent.trim();
      }
      
      return '';
    };

    return {
      score_formula: extractField('score_formula'),
      competition_analysis: extractField('competition_analysis'),
      study_suggestions: extractField('study_suggestions')
    };
  }

  /**
   * 停止分析
   */
  stopAnalysis() {
    this.isAnalyzing = false;
    this.webSocket.close();
    this.typingEffect.stopAllTyping();
  }

  /**
   * 获取分析状态
   */
  getAnalysisStatus(): boolean {
    return this.isAnalyzing;
  }

  /**
   * 获取WebSocket连接状态
   */
  getConnectionStatus(): boolean {
    return this.webSocket.getConnectionStatus();
  }

  /**
   * 获取打字机效果状态
   */
  getTypingStatus(): boolean {
    return this.typingEffect.isTyping();
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.stopAnalysis();
    this.typingEffect.destroy();
  }
}
