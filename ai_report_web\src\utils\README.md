# WebSocket 和打字机效果工具类

这个目录包含了用于处理单个学校分析的 WebSocket 通信和打字机效果的工具类。

## 文件说明

### 1. SingleSchoolWebSocket.ts
WebSocket 通信处理类，负责与后端的 WebSocket 服务器通信。

**主要功能：**
- 建立 WebSocket 连接
- 发送学校分析请求
- 接收流式数据
- 解析文本格式的返回数据
- 连接状态管理

**使用示例：**
```typescript
import { SingleSchoolWebSocket } from '@/utils/SingleSchoolWebSocket';

const webSocket = new SingleSchoolWebSocket('ws://127.0.0.1:8791');

webSocket.setCallbacks({
  onConnected: () => console.log('连接成功'),
  onStart: (message) => console.log('开始分析:', message),
  onData: (data) => console.log('收到数据:', data),
  onComplete: () => console.log('分析完成'),
  onError: (error) => console.error('错误:', error)
});

await webSocket.analyzeSchool('报告ID', {
  id: '学校ID',
  school_name: '学校名称',
  college_name: '学院名称'
});
```

### 2. TypingEffect.ts
打字机效果处理类，提供文本的打字机动画效果。

**主要功能：**
- 单个文本打字机效果
- 多个文本同时打字机效果
- 打字速度控制
- 进度回调
- 效果管理和清理

**使用示例：**
```typescript
import { TypingEffect } from '@/utils/TypingEffect';

const typingEffect = new TypingEffect();

// 启动单个打字机效果
typingEffect.startTyping('id1', '要显示的文本', {
  callback: (text) => {
    // 更新界面显示
    document.getElementById('target').textContent = text;
  }
}, {
  speed: 50,
  onComplete: () => console.log('打字完成')
});

// 同时启动多个打字机效果
typingEffect.startMultipleTyping([
  {
    id: 'formula',
    text: '总成绩计算公式内容...',
    target: { callback: (text) => updateFormula(text) }
  },
  {
    id: 'analysis',
    text: '竞争难度分析内容...',
    target: { callback: (text) => updateAnalysis(text) }
  }
]);
```

### 3. SchoolAnalysisManager.ts
学校分析管理类，整合了 WebSocket 通信和打字机效果。

**主要功能：**
- 统一管理 WebSocket 连接和打字机效果
- 简化的 API 接口
- 自动资源管理
- 状态监控

**使用示例：**
```typescript
import { SchoolAnalysisManager } from '@/utils/SchoolAnalysisManager';

const manager = new SchoolAnalysisManager();

// 设置回调函数
manager.setCallbacks({
  onStart: (message) => {
    ElMessage.success(message);
  },
  onProgress: (result) => {
    // 实时更新界面
    if (result.score_formula) {
      updateScoreFormula(result.score_formula);
    }
    if (result.difficulty_analysis) {
      updateDifficultyAnalysis(result.difficulty_analysis);
    }
    if (result.suggest) {
      updateSuggestion(result.suggest);
    }
  },
  onComplete: (result) => {
    ElMessage.success('分析完成');
    console.log('最终结果:', result);
  },
  onError: (error) => {
    ElMessage.error('分析失败: ' + error);
  }
});

// 开始分析
try {
  await manager.analyzeSchool('报告ID', {
    id: '学校ID',
    school_name: '学校名称',
    college_name: '学院名称'
  });
} catch (error) {
  console.error('分析失败:', error);
}

// 组件销毁时清理资源
onUnmounted(() => {
  manager.destroy();
});
```

## 数据格式

### 输入数据格式
```typescript
interface SchoolInfo {
  id: string | number;
  school_name: string;
  major_name?: string;
  college_name?: string;
}
```

### 输出数据格式
```typescript
interface SchoolAnalysisData {
  score_formula: string;        // 总成绩计算公式
  competition_analysis: string; // 竞争难度分析
  study_suggestions: string;    // 备考目标建议
}
```

### WebSocket 消息格式
```typescript
interface WebSocketMessage {
  type: string;           // 消息类型：connected, start, stream_data, complete, error
  message?: string;       // 消息内容
  content?: string;       // 流式数据内容
  connection_id?: string; // 连接ID
}
```

## 后端数据格式

后端返回的文本格式如下：
```
score_formula: "总成绩计算公式: 具体的计算公式内容...",
competition_analysis: "竞争难度分析: 具体的分析内容...",
study_suggestions: "备考目标建议: 具体的建议内容..."
```

## 注意事项

1. **资源清理**：使用完毕后务必调用 `destroy()` 方法清理资源
2. **错误处理**：设置适当的错误回调处理异常情况
3. **连接状态**：可以通过相应的状态方法检查连接和处理状态
4. **打字速度**：可以根据需要调整打字机效果的速度
5. **并发限制**：同时只能进行一个学校的分析

## 集成到 Vue 组件

在 Vue 组件中使用时的完整示例：

```vue
<script setup lang="ts">
import { ref, onUnmounted } from 'vue';
import { SchoolAnalysisManager } from '@/utils/SchoolAnalysisManager';
import { ElMessage } from 'element-plus';

// 创建管理器实例
const analysisManager = new SchoolAnalysisManager();

// 响应式数据
const scoreFormula = ref('');
const difficultyAnalysis = ref('');
const suggestion = ref('');

// 设置回调
analysisManager.setCallbacks({
  onStart: (message) => {
    ElMessage.success(message);
  },
  onProgress: (result) => {
    scoreFormula.value = result.score_formula;
    difficultyAnalysis.value = result.difficulty_analysis;
    suggestion.value = result.suggest;
  },
  onComplete: (result) => {
    ElMessage.success('分析完成');
  },
  onError: (error) => {
    ElMessage.error('分析失败: ' + error);
  }
});

// 分析学校
const analyzeSchool = async (school) => {
  try {
    await analysisManager.analyzeSchool(reportId.value, school);
  } catch (error) {
    console.error('分析失败:', error);
  }
};

// 清理资源
onUnmounted(() => {
  analysisManager.destroy();
});
</script>
```

这样的封装使得 WebSocket 通信和打字机效果的使用变得更加简单和可维护。
