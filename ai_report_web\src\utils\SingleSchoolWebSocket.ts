/**
 * 单个学校分析WebSocket处理类
 */
export interface SchoolAnalysisData {
  score_formula: string;
  competition_analysis: string;
  study_suggestions: string;
}

export interface WebSocketMessage {
  type: string;
  message?: string;
  content?: string;
  connection_id?: string;
}

export interface SchoolInfo {
  id: string | number;
  school_name: string;
  major_name?: string;
  college_name?: string;
}

export class SingleSchoolWebSocket {
  private ws: WebSocket | null = null;
  private wsUrl: string;
  private accumulatedContent: string = '';
  private isConnected: boolean = false;

  // 回调函数
  private onConnectedCallback?: () => void;
  private onStartCallback?: (message: string) => void;
  private onDataCallback?: (data: SchoolAnalysisData) => void;
  private onCompleteCallback?: () => void;
  private onErrorCallback?: (error: string) => void;
  private onCloseCallback?: () => void;

  constructor(wsUrl: string = 'ws://127.0.0.1:8791') {
    this.wsUrl = wsUrl;
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks: {
    onConnected?: () => void;
    onStart?: (message: string) => void;
    onData?: (data: SchoolAnalysisData) => void;
    onComplete?: () => void;
    onError?: (error: string) => void;
    onClose?: () => void;
  }) {
    this.onConnectedCallback = callbacks.onConnected;
    this.onStartCallback = callbacks.onStart;
    this.onDataCallback = callbacks.onData;
    this.onCompleteCallback = callbacks.onComplete;
    this.onErrorCallback = callbacks.onError;
    this.onCloseCallback = callbacks.onClose;
  }

  /**
   * 连接WebSocket并发送学校信息
   */
  async analyzeSchool(reportId: string | number, schoolInfo: SchoolInfo): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log('创建WebSocket连接:', this.wsUrl);
        this.ws = new WebSocket(this.wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立');
          this.isConnected = true;
          this.accumulatedContent = '';

          if (this.onConnectedCallback) {
            this.onConnectedCallback();
          }

          // 发送学校信息
          const message = {
            report_id: reportId,
            school_name: schoolInfo.school_name,
            college_name: schoolInfo.college_name || schoolInfo.major_name || '未知学院'
          };

          this.ws!.send(JSON.stringify(message));
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          const errorMessage = 'WebSocket连接错误';
          if (this.onErrorCallback) {
            this.onErrorCallback(errorMessage);
          }
          reject(new Error(errorMessage));
        };

        this.ws.onclose = () => {
          console.log('WebSocket连接已关闭');
          this.isConnected = false;
          this.accumulatedContent = '';
          
          if (this.onCloseCallback) {
            this.onCloseCallback();
          }
        };

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 处理WebSocket消息
   */
  private handleMessage(data: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data);
      console.log('收到WebSocket消息:', message);

      switch (message.type) {
        case 'connected':
          console.log('WebSocket连接确认:', message.message);
          break;

        case 'start':
          console.log('开始处理:', message.message);
          if (this.onStartCallback && message.message) {
            this.onStartCallback(message.message);
          }
          break;

        case 'stream_data':
          if (message.content) {
            this.handleStreamData(message.content);
          }
          break;

        case 'complete':
          console.log('处理完成:', message.message);
          if (this.onCompleteCallback) {
            this.onCompleteCallback();
          }
          this.close();
          break;

        case 'error':
          console.error('WebSocket错误:', message.message);
          if (this.onErrorCallback && message.message) {
            this.onErrorCallback(message.message);
          }
          this.close();
          break;

        default:
          console.log('未知消息类型:', message.type, message);
          break;
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  /**
   * 处理流式数据
   */
  private handleStreamData(content: string) {
    // 累积内容
    this.accumulatedContent += content;
    console.log('收到流式数据:', content);

    // 解析文本格式的数据并触发回调
    const analysisData = this.parseTextFormatData(this.accumulatedContent);
    
    if (this.onDataCallback) {
      this.onDataCallback(analysisData);
    }
  }

  /**
   * 解析文本格式的数据
   */
  private parseTextFormatData(fullContent: string): SchoolAnalysisData {
    return {
      score_formula: this.extractFieldContent(fullContent, 'score_formula'),
      competition_analysis: this.extractFieldContent(fullContent, 'competition_analysis'),
      study_suggestions: this.extractFieldContent(fullContent, 'study_suggestions')
    };
  }

  /**
   * 提取字段内容
   */
  private extractFieldContent(content: string, fieldName: string): string {
    // 查找字段开始位置
    const fieldPattern = new RegExp(`${fieldName}:\\s*"([^"]*)"`, 'i');
    const match = content.match(fieldPattern);

    if (match && match[1]) {
      // 提取引号内的内容，并处理转义字符
      let fieldContent = match[1];

      // 处理常见的转义字符
      fieldContent = fieldContent.replace(/\\n/g, '\n');
      fieldContent = fieldContent.replace(/\\"/g, '"');
      fieldContent = fieldContent.replace(/\\\\/g, '\\');

      return fieldContent.trim();
    }

    return '';
  }

  /**
   * 关闭WebSocket连接
   */
  close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.accumulatedContent = '';
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * 获取累积的内容
   */
  getAccumulatedContent(): string {
    return this.accumulatedContent;
  }
}
