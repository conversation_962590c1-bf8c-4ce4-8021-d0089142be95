<?php

namespace app\controller;

use support\Log;
use Workerman\Connection\TcpConnection;
use Workerman\Worker;
use Workerman\Timer;

class SingleSchoolWebSocketController
{
    /**
     * WebSocket 连接建立时触发
     */
    public function onConnect(TcpConnection $connection)
    {
        Log::info('WebSocket连接建立', ['connection_id' => $connection->id]);
        
        // 发送连接成功消息
        $connection->send(json_encode([
            'type' => 'connected',
            'message' => 'WebSocket连接成功',
            'connection_id' => $connection->id
        ]));
    }

    /**
     * 接收到消息时触发
     */
    public function onMessage(TcpConnection $connection, $data)
    {
        try {
            $message = json_decode($data, true);
            
            if (!$message) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '消息格式错误'
                ]));
                return;
            }

            Log::info('收到WebSocket消息', $message);

            // 验证必要参数
            if (!isset($message['report_id']) || !isset($message['school_name']) || !isset($message['college_name'])) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '缺少必要参数：report_id, school_name, college_name'
                ]));
                return;
            }

            // 处理单个学校分析请求
            $this->processSingleSchoolAnalysis($connection, $message);

        } catch (\Exception $e) {
            Log::error('WebSocket消息处理错误', ['error' => $e->getMessage()]);
            $connection->send(json_encode([
                'type' => 'error',
                'message' => '消息处理失败：' . $e->getMessage()
            ]));
        }
    }

    /**
     * 连接关闭时触发
     */
    public function onClose(TcpConnection $connection)
    {
        Log::info('WebSocket连接关闭', ['connection_id' => $connection->id]);
    }

    /**
     * 处理单个学校分析
     */
    private function processSingleSchoolAnalysis(TcpConnection $connection, $message)
    {
        $reportId = $message['report_id'];
        $schoolName = $message['school_name'];
        $collegeName = $message['college_name'];

        try {
            // 从数据库获取 context
            $context = db("school_report")->where("id", $reportId)->value("context");
            
            if (!$context) {
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '未找到报告数据'
                ]));
                return;
            }

            // 截取"目标偏好："之前的数据
            $targetPreferencePos = strpos($context, '目标偏好：');
            if ($targetPreferencePos === false) {
                $baseContext = $context;
            } else {
                $baseContext = substr($context, 0, $targetPreferencePos);
            }

            // 拼接新的学校和学院信息
            $newContext = $baseContext . "目标偏好：\n";
            $newContext .= "目标学校：{$schoolName}\n";
            $newContext .= "目标学院：{$collegeName}\n";

            Log::info('构建的新context', ['context' => $newContext]);

            // 发送开始处理消息
            $connection->send(json_encode([
                'type' => 'start',
                'message' => '开始分析学校信息...'
            ]));

            // 调用千问API进行流式处理
            $this->streamQianwenApiForSingleSchool($newContext, function($chunk) use ($connection) {
                // 发送流式数据
                $connection->send(json_encode([
                    'type' => 'data',
                    'content' => $chunk
                ]));
            }, function() use ($connection) {
                // 流式处理完成
                $connection->send(json_encode([
                    'type' => 'complete',
                    'message' => '分析完成'
                ]));
            });

        } catch (\Exception $e) {
            Log::error('单个学校分析处理错误', ['error' => $e->getMessage()]);
            $connection->send(json_encode([
                'type' => 'error',
                'message' => '分析处理失败：' . $e->getMessage()
            ]));
        }
    }

    /**
     * 调用千问API进行流式处理（单个学校版本）
     */
    private function streamQianwenApiForSingleSchool($content, $dataCallback, $completeCallback)
    {
        try {
            // 获取单个学校配置
            $appId = config('qianwen.single_school')['appid'];
            $apiKey = config('qianwen.single_school')['key'];
            
            if (empty($appId) || empty($apiKey)) {
                throw new \Exception('千问API单个学校配置信息不完整');
            }
            
            $url = "https://dashscope.aliyuncs.com/api/v1/apps/$appId/completion";
            
            // 构建请求数据
            $data = [
                "input" => [
                    'prompt' => $content
                ],
                "parameters" => [
                    "incremental_output" => true
                ]
            ];
            
            $dataString = json_encode($data);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("JSON encoding failed with error: " . json_last_error_msg());
            }
            
            Log::info('千问API单个学校请求', ['url' => $url, 'data' => $dataString]);
            
            // 初始化 cURL
            $ch = curl_init($url);
            
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'X-DashScope-SSE: enable'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 600);
            
            // 设置流式处理回调
            $buffer = '';
            $fullContent = '';
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl_handle, $data) use ($dataCallback, &$buffer, &$fullContent) {
                $buffer .= $data;
                $lines = explode("\n", $buffer);
                $buffer = array_pop($lines); // 保留最后一行（可能不完整）

                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    if (strpos($line, 'data:') === 0) {
                        $jsonData = trim(substr($line, 5));
                        if ($jsonData === '[DONE]') {
                            continue;
                        }

                        $decoded = json_decode($jsonData, true);
                        if ($decoded && isset($decoded['output']['text'])) {
                            $text = $decoded['output']['text'];
                            $fullContent = $text; // 累积完整内容
                            $dataCallback($fullContent);
                        }
                    }
                }

                return strlen($data);
            });
            
            // 执行请求
            curl_exec($ch);

            if (curl_error($ch)) {
                throw new \Exception('cURL错误: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            // 调用完成回调
            $completeCallback();
            
        } catch (\Exception $e) {
            Log::error('千问API单个学校调用失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
